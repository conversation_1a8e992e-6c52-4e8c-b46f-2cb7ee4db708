# ========================
# Supabase Configuration
# ========================

# Supabase Project URL
NEXT_PUBLIC_SUPABASE_URL="https://your-supabase-project.supabase.co"

# Supabase Anon Key (Public Key)
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"

# Supabase Service Role Key (Private Key)
SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key"

# ========================
# AI API Keys
# ========================

# Gemini API Key
GEMINI_API_KEY="your-gemini-api-key"

# OpenAI API Key
OPENAI_API_KEY="your-openai-api-key"

# DeepSeek API Key
DEEPSEEK_API_KEY="your-deepseek-api-key"

# OpenRouter API Key
OPENROUTER_API_KEY="your-openrouter-api-key"

# Anthropic API Key
ANTHROPIC_API_KEY="your-anthropic-api-key"

# Groq API Key
GROQ_API_KEY="your-groq-api-key"

# ========================
# Redis Configuration
# ========================

# Upstash Redis REST URL
UPSTASH_REDIS_REST_URL="https://your-redis-instance.upstash.io"

# Upstash Redis REST Token
UPSTASH_REDIS_REST_TOKEN="your-upstash-redis-token"

# ========================
# Stripe Configuration
# ========================

# Stripe Publishable Key (Public Key)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="your-stripe-publishable-key"

# Stripe Secret Key (Private Key)
STRIPE_SECRET_KEY="your-stripe-secret-key"

# Stripe Webhook Secret
STRIPE_WEBHOOK_SECRET="your-stripe-webhook-secret"

# Stripe Product ID for Subscription
PRODUCT_ID="your-stripe-product-id"

# Stripe Price ID for Subscription
PRICE_ID="your-stripe-price-id"

# Stripe Pro Price ID
NEXT_PUBLIC_STRIPE_PRO_PRICE_ID="your-stripe-pro-price-id"

# Subscription Link
NEXT_PUBLIC_SUBSCRIPTION_LINK="https://your-stripe-subscription-link"

# ========================
# Application Configuration
# ========================

# Site URL (Development)
NEXT_PUBLIC_SITE_URL="http://localhost:3000"

# Site URL (Production)
# NEXT_PUBLIC_SITE_URL="https://your-production-url.com"
 